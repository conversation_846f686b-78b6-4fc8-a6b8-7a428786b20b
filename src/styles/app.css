@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
@layer base {
	html {
		color-scheme: light dark;
		font-feature-settings: "cv02", "cv03", "cv04", "cv11";
		scroll-behavior: smooth;
	}

	* {
		@apply border-gray-200 dark:border-gray-800;
	}

	html,
	body {
		@apply text-gray-900 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900 dark:text-gray-100;
		font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
		line-height: 1.6;
		letter-spacing: -0.01em;
	}

	.using-mouse * {
		outline: none !important;
	}

	*:focus-visible {
		@apply ring-2 ring-primary ring-offset-2 ring-offset-background;
	}

	button, a, input, textarea, select {
		@apply transition-all duration-200 ease-in-out;
	}

	::-webkit-scrollbar {
		width: 8px;
	}

	::-webkit-scrollbar-track {
		@apply bg-gray-100 dark:bg-gray-800;
	}

	::-webkit-scrollbar-thumb {
		@apply bg-gray-300 dark:bg-gray-600 rounded-full;
	}

	::-webkit-scrollbar-thumb:hover {
		@apply bg-gray-400 dark:bg-gray-500;
	}
}

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

:root {
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.147 0.004 49.25);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.147 0.004 49.25);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.147 0.004 49.25);
	--primary: oklch(0.216 0.006 56.043);
	--primary-foreground: oklch(0.985 0.001 106.423);
	--secondary: oklch(0.97 0.001 106.424);
	--secondary-foreground: oklch(0.216 0.006 56.043);
	--muted: oklch(0.97 0.001 106.424);
	--muted-foreground: oklch(0.553 0.013 58.071);
	--accent: oklch(0.97 0.001 106.424);
	--accent-foreground: oklch(0.216 0.006 56.043);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.923 0.003 48.717);
	--input: oklch(0.923 0.003 48.717);
	--ring: oklch(0.709 0.01 56.259);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0.001 106.423);
	--sidebar-foreground: oklch(0.147 0.004 49.25);
	--sidebar-primary: oklch(0.216 0.006 56.043);
	--sidebar-primary-foreground: oklch(0.985 0.001 106.423);
	--sidebar-accent: oklch(0.97 0.001 106.424);
	--sidebar-accent-foreground: oklch(0.216 0.006 56.043);
	--sidebar-border: oklch(0.923 0.003 48.717);
	--sidebar-ring: oklch(0.709 0.01 56.259);
}

.dark {
	--background: oklch(0.147 0.004 49.25);
	--foreground: oklch(0.985 0.001 106.423);
	--card: oklch(0.216 0.006 56.043);
	--card-foreground: oklch(0.985 0.001 106.423);
	--popover: oklch(0.216 0.006 56.043);
	--popover-foreground: oklch(0.985 0.001 106.423);
	--primary: oklch(0.923 0.003 48.717);
	--primary-foreground: oklch(0.216 0.006 56.043);
	--secondary: oklch(0.268 0.007 34.298);
	--secondary-foreground: oklch(0.985 0.001 106.423);
	--muted: oklch(0.268 0.007 34.298);
	--muted-foreground: oklch(0.709 0.01 56.259);
	--accent: oklch(0.268 0.007 34.298);
	--accent-foreground: oklch(0.985 0.001 106.423);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.553 0.013 58.071);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.216 0.006 56.043);
	--sidebar-foreground: oklch(0.985 0.001 106.423);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0.001 106.423);
	--sidebar-accent: oklch(0.268 0.007 34.298);
	--sidebar-accent-foreground: oklch(0.985 0.001 106.423);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.553 0.013 58.071);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
}

/* Custom utility classes */
@layer utilities {
	/* Text truncation */
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Animation utilities */
	.animate-fade-in {
		animation: fadeIn 0.5s ease-in-out;
	}

	.animate-slide-up {
		animation: slideUp 0.3s ease-out;
	}

	.animate-bounce-subtle {
		animation: bounceSubtle 0.6s ease-in-out;
	}

	/* Glass morphism effect */
	.glass {
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.2);
	}

	.dark .glass {
		background: rgba(0, 0, 0, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.1);
	}
}

/* Keyframe animations */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideUp {
	from {
		transform: translateY(20px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes bounceSubtle {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-5px);
	}
	60% {
		transform: translateY(-3px);
	}
}
